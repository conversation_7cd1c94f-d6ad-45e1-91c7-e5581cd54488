# Streamable HTTP MCP Server

A next-generation Model Context Protocol (MCP) server that provides real-time screen capture streaming using the innovative Streamable HTTP protocol with HTTP/2 optimization and chunked transfer encoding.

## Features

- 🖥️ **Cross-platform screen capture** (Windows, macOS, Linux)
- 🚀 **Streamable HTTP Protocol** - Next-generation transport layer
- ⚡ **HTTP/2 Multiplexing** - Multiple streams over single connection
- 📦 **Chunked Transfer Encoding** - Efficient real-time data streaming
- 🔄 **Adaptive Quality Control** - Dynamic quality adjustment based on performance
- 📊 **Advanced Compression** (none, gzip, zstd) with optimization
- 🖼️ **Multiple image formats** (JPEG, PNG, WebP)
- 📈 **Performance monitoring** and detailed statistics
- 🛡️ **Full MCP compliance** with enhanced streaming capabilities
- 🔌 **Backward compatibility** with WebSocket transport

## Quick Start

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd base64_streaming_mcp
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

### Claude Desktop Integration

To use with <PERSON>, add this configuration to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "base64-streaming": {
      "command": "python",
      "args": ["-m", "src.server"],
      "cwd": "/path/to/base64_streaming_mcp",
      "env": {
        "MCP_HOST": "localhost",
        "MCP_PORT": "7788"
      }
    }
  }
}
```

**Windows Example:**
```json
{
  "mcpServers": {
    "base64-streaming": {
      "command": "python",
      "args": ["-m", "src.server"],
      "cwd": "C:\\Users\\<USER>\\base64_streaming_mcp",
      "env": {
        "MCP_HOST": "localhost",
        "MCP_PORT": "7788"
      }
    }
  }
}
```

Once configured, you can use natural language with Claude:
- "Can you capture my current screen?"
- "Start streaming my screen at 15 FPS"
- "Show me available monitors"
- "Setup optimal streaming for presentation"

### Basic Usage

1. Start the Streamable HTTP MCP server:
```bash
python -m src.main
```

2. Or with custom configuration:
```bash
python -m src.main --host 0.0.0.0 --port 8080 --fps 30 --quality 85
```

3. Connect via HTTP endpoints:
```bash
# MCP Messages: http://localhost:8080/mcp/message
# MCP Stream: http://localhost:8080/mcp/stream
# Screen Stream: http://localhost:8080/stream/screen
# Health Check: http://localhost:8080/health
```

4. Legacy WebSocket support:
```bash
# WebSocket: ws://localhost:7788 (if enabled)
```

### Configuration

Create a `config.json` file:
```json
{
  "host": "localhost",
  "port": 8080,
  "transport_type": "streamable_http",
  "fps": 30,
  "compression": "gzip",
  "image_format": "jpeg",
  "jpeg_quality": 85,
  "max_width": 1920,
  "max_height": 1080,
  "enable_compression": true,
  "chunk_size": 65536
}
```

Or use command line arguments:
```bash
python run_server.py --host 0.0.0.0 --port 8080 --fps 30 --quality 85 --compression gzip
```

## MCP Protocol Support

### Resources

- `screen://current` - Current screen capture as base64 image
- `stream://status` - Live stream status and statistics
- `info://monitors` - Available monitors information

### Tools

- `start_stream` - Start real-time screen streaming with quality control
- `stop_stream` - Stop screen streaming
- `capture_screenshot` - Capture single screenshot
- `get_stream_status` - Get current streaming status and statistics
- `list_monitors` - List available monitors
- `update_config` - Update streaming configuration

### Streaming Endpoints

- `/mcp/message` - JSON-RPC 2.0 message handling
- `/mcp/stream` - MCP streaming connection
- `/stream/screen` - Direct screen capture streaming
- `/stream/events` - Server-Sent Events
- `/health` - Health check endpoint
- `/status` - Server status and metrics

## HTTP API

### Endpoints

#### Health Check
```bash
GET /health
```
Returns server health status and basic information.

#### Server Status
```bash
GET /status
```
Returns detailed server status, performance metrics, and connection information.

#### MCP JSON-RPC Messages
```bash
POST /mcp/message
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "start_stream",
    "arguments": {
      "fps": 30,
      "quality": "adaptive"
    }
  }
}
```

#### Screen Streaming
```bash
GET /stream/screen
```
Returns chunked HTTP stream of screen capture data.

#### Server-Sent Events
```bash
GET /stream/events
```
Returns real-time server events via SSE.

### Example Usage with curl

```bash
# Health check
curl http://localhost:8080/health

# Start streaming
curl -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "start_stream",
      "arguments": {"fps": 15, "quality": "medium"}
    }
  }'

# Get stream status
curl -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/call",
    "params": {
      "name": "get_stream_status",
      "arguments": {}
    }
  }'
```

## Performance Optimization

### Recommended Settings

**High Quality (Presentations):**
- FPS: 30-60
- Compression: zstd
- Format: png
- Quality: 95

**Balanced (General Use):**
- FPS: 20-30
- Compression: gzip
- Format: jpeg
- Quality: 85

**Low Bandwidth (Remote Access):**
- FPS: 10-15
- Compression: gzip
- Format: jpeg
- Quality: 60
- Max Resolution: 1280x720

### Platform-Specific Optimizations

**Windows:**
- Enable threading
- Larger chunk sizes (128KB)

**macOS:**
- Lower FPS (25) for better performance
- Use zstd compression

**Linux:**
- Enable threading
- Use gzip compression

## Command Line Options

```bash
python run_server.py [OPTIONS]

Options:
  --host TEXT              Server host [default: localhost]
  --port INTEGER           Server port [default: 8080]
  --fps INTEGER            Frames per second [default: 30]
  --quality INTEGER        JPEG quality 1-100 [default: 85]
  --compression TEXT       Compression type (none, gzip, zstd) [default: gzip]
  --config PATH            Configuration file path (JSON format)
  --log-level TEXT         Logging level (DEBUG, INFO, WARNING, ERROR) [default: INFO]
  --version                Show version information
  --help                   Show help message
```

### Examples

```bash
# Basic usage
python run_server.py

# Custom configuration
python run_server.py --host 0.0.0.0 --port 8080 --fps 15 --quality 90

# High performance setup
python run_server.py --fps 60 --quality 95 --compression zstd

# Low bandwidth setup
python run_server.py --fps 10 --quality 60 --compression gzip
```

## Examples

See the `examples/` directory for:
- `client_example.py` - HTTP client with MCP protocol support
- Test the server with the included client

### Running the Client Example

```bash
# Start the server first
python run_server.py

# In another terminal, run the client
python examples/client_example.py
```

## Development

### Running Tests

```bash
# Install test dependencies
pip install -r requirements.txt

# Run tests
python -m pytest tests/test_streamable_http.py -v
```

### Code Formatting

```bash
black src/
flake8 src/
mypy src/
```

## Troubleshooting

### Common Issues

1. **Permission Errors (macOS/Linux):**
   - Grant screen recording permissions
   - Run with appropriate privileges

2. **High CPU Usage:**
   - Reduce FPS
   - Lower image quality
   - Use more compression

3. **Memory Issues:**
   - Reduce buffer size
   - Lower resolution
   - Enable memory limits

4. **Connection Issues:**
   - Check firewall settings
   - Verify port availability (default: 8080)
   - Test with curl: `curl http://localhost:8080/health`

### Logging

Enable debug logging:
```bash
python run_server.py --log-level DEBUG
```

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Support

For issues and questions:
- Create an issue on GitHub
- Check the troubleshooting guide
- Review the examples

## Changelog

### v2.0.0 (Current)
- **🚀 Streamable HTTP Protocol** - Complete migration from WebSocket to HTTP/2
- **⚡ HTTP/2 Multiplexing** - Multiple streams over single connection
- **📦 Chunked Transfer Encoding** - Efficient real-time data streaming
- **🔄 Adaptive Quality Control** - Dynamic quality adjustment
- **📈 Enhanced Performance** - Better compression and optimization
- **🛡️ Improved Error Handling** - HTTP status codes and detailed errors
- **🔌 Standard HTTP Infrastructure** - Proxy and load balancer compatibility

### v1.0.0 (Legacy)
- Initial release with WebSocket transport
- Cross-platform screen capture
- Basic MCP protocol support
- Multiple compression options

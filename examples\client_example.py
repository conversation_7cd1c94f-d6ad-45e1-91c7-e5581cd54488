#!/usr/bin/env python3
"""
Example WebSocket client for base64_streaming_mcp server.
Demonstrates how to connect and receive screen streaming data.
"""

import asyncio
import json
import base64
import time
from typing import Optional
import websockets
from websockets.client import WebSocketClientProtocol


class StreamingClient:
    """Example client for MCP screen streaming server."""
    
    def __init__(self, uri: str = "ws://localhost:7788"):
        """Initialize client with server URI."""
        self.uri = uri
        self.websocket: Optional[WebSocketClientProtocol] = None
        self.client_id: Optional[str] = None
        self.running = False
        self.frame_count = 0
        self.start_time = 0.0
    
    async def connect(self):
        """Connect to the streaming server."""
        try:
            print(f"Connecting to {self.uri}...")
            self.websocket = await websockets.connect(self.uri)
            print("Connected successfully!")
            
            # Wait for welcome message
            welcome_msg = await self.websocket.recv()
            welcome_data = json.loads(welcome_msg)
            
            if welcome_data.get("type") == "welcome":
                self.client_id = welcome_data.get("client_id")
                print(f"Received welcome message. Client ID: {self.client_id}")
                print(f"Server: {welcome_data.get('server')} v{welcome_data.get('version')}")
                print(f"Capabilities: {welcome_data.get('capabilities')}")
                return True
            else:
                print(f"Unexpected welcome message: {welcome_data}")
                return False
                
        except Exception as e:
            print(f"Connection failed: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from the server."""
        if self.websocket:
            await self.websocket.close()
            print("Disconnected from server")
    
    async def send_message(self, message: dict):
        """Send a message to the server."""
        if not self.websocket:
            print("Not connected to server")
            return
        
        try:
            await self.websocket.send(json.dumps(message))
        except Exception as e:
            print(f"Failed to send message: {e}")
    
    async def start_stream(self, fps: int = 10, compression: str = "gzip", format: str = "jpeg"):
        """Start screen streaming."""
        message = {
            "type": "start_stream",
            "fps": fps,
            "compression": compression,
            "format": format
        }
        await self.send_message(message)
        print(f"Requested stream start: {fps} FPS, {compression} compression, {format} format")
    
    async def stop_stream(self):
        """Stop screen streaming."""
        message = {"type": "stop_stream"}
        await self.send_message(message)
        print("Requested stream stop")
    
    async def get_single_frame(self):
        """Request a single frame."""
        message = {"type": "get_frame"}
        await self.send_message(message)
        print("Requested single frame")
    
    async def get_status(self):
        """Get server status."""
        message = {"type": "get_status"}
        await self.send_message(message)
        print("Requested server status")
    
    async def ping(self):
        """Send ping to server."""
        message = {"type": "ping"}
        await self.send_message(message)
        print("Sent ping")
    
    async def listen_for_messages(self):
        """Listen for messages from the server."""
        if not self.websocket:
            return
        
        self.running = True
        self.start_time = time.time()
        
        try:
            async for message in self.websocket:
                if not self.running:
                    break
                
                try:
                    data = json.loads(message)
                    await self.handle_message(data)
                except json.JSONDecodeError as e:
                    print(f"Failed to parse message: {e}")
                except Exception as e:
                    print(f"Error handling message: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            print("Connection closed by server")
        except Exception as e:
            print(f"Error in message listener: {e}")
        finally:
            self.running = False
    
    async def handle_message(self, data: dict):
        """Handle incoming messages from server."""
        msg_type = data.get("type")
        
        if msg_type == "stream_started":
            print(f"Stream started successfully!")
            print(f"Configuration: {data.get('config')}")
            
        elif msg_type == "stream_stopped":
            print("Stream stopped")
            
        elif msg_type == "stream_frame":
            await self.handle_stream_frame(data)
            
        elif msg_type == "frame":
            await self.handle_single_frame(data)
            
        elif msg_type == "status":
            await self.handle_status(data)
            
        elif msg_type == "pong":
            print("Received pong")
            
        elif msg_type == "error":
            print(f"Server error: {data.get('message')}")
            
        else:
            print(f"Unknown message type: {msg_type}")
    
    async def handle_stream_frame(self, data: dict):
        """Handle streaming frame data."""
        self.frame_count += 1
        frame_id = data.get("frame_id")
        timestamp = data.get("timestamp")
        format_type = data.get("format")
        compression = data.get("compression")
        size = data.get("size")
        compressed_size = data.get("compressed_size")
        
        # Calculate FPS
        elapsed = time.time() - self.start_time
        fps = self.frame_count / elapsed if elapsed > 0 else 0
        
        print(f"Frame {frame_id}: {format_type}, {compression}, "
              f"{size} bytes -> {compressed_size} bytes, "
              f"FPS: {fps:.1f}")
        
        # Optionally save frame to file
        if self.frame_count <= 5:  # Save first 5 frames as example
            await self.save_frame(data, frame_id)
    
    async def handle_single_frame(self, data: dict):
        """Handle single frame data."""
        format_type = data.get("format")
        size = data.get("size")
        timestamp = data.get("timestamp")
        
        print(f"Single frame received: {format_type}, {size} bytes, timestamp: {timestamp}")
        
        # Save the frame
        await self.save_frame(data, "single")
    
    async def handle_status(self, data: dict):
        """Handle status response."""
        stream_info = data.get("stream_info", {})
        active_streams = data.get("active_streams")
        connections = data.get("connections")
        
        print(f"Server Status:")
        print(f"  Active streams: {active_streams}")
        print(f"  Total connections: {connections}")
        print(f"  Stream state: {stream_info.get('state')}")
        
        if "stats" in stream_info:
            stats = stream_info["stats"]
            print(f"  Total frames: {stats.get('total_frames')}")
            print(f"  Average FPS: {stats.get('avg_fps', 0):.1f}")
            print(f"  Compression ratio: {stats.get('avg_compression_ratio', 1.0):.2f}")
    
    async def save_frame(self, data: dict, frame_id):
        """Save frame data to file."""
        try:
            base64_data = data.get("data")
            format_type = data.get("format", "jpeg")
            
            if base64_data:
                # Decode base64 data
                image_data = base64.b64decode(base64_data)
                
                # Save to file
                filename = f"frame_{frame_id}.{format_type}"
                with open(filename, "wb") as f:
                    f.write(image_data)
                
                print(f"Saved frame to {filename} ({len(image_data)} bytes)")
                
        except Exception as e:
            print(f"Failed to save frame: {e}")
    
    def stop_listening(self):
        """Stop listening for messages."""
        self.running = False


async def main():
    """Main example function."""
    client = StreamingClient()
    
    # Connect to server
    if not await client.connect():
        return
    
    try:
        # Start listening for messages in background
        listen_task = asyncio.create_task(client.listen_for_messages())
        
        # Wait a bit for connection to stabilize
        await asyncio.sleep(1)
        
        # Example 1: Get server status
        print("\n=== Getting server status ===")
        await client.get_status()
        await asyncio.sleep(2)
        
        # Example 2: Capture single frame
        print("\n=== Capturing single frame ===")
        await client.get_single_frame()
        await asyncio.sleep(3)
        
        # Example 3: Start streaming for 10 seconds
        print("\n=== Starting stream for 10 seconds ===")
        await client.start_stream(fps=5, compression="gzip", format="jpeg")
        await asyncio.sleep(10)
        
        # Stop streaming
        print("\n=== Stopping stream ===")
        await client.stop_stream()
        await asyncio.sleep(2)
        
        # Example 4: Ping test
        print("\n=== Ping test ===")
        await client.ping()
        await asyncio.sleep(1)
        
        # Stop listening
        client.stop_listening()
        await listen_task
        
    except KeyboardInterrupt:
        print("\nInterrupted by user")
        client.stop_listening()
    except Exception as e:
        print(f"Error in main: {e}")
    finally:
        await client.disconnect()


if __name__ == "__main__":
    print("Base64 Streaming MCP Client Example")
    print("===================================")
    asyncio.run(main())

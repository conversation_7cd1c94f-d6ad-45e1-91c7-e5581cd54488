"""
Streamable HTTP MCP Server Package

A next-generation Model Context Protocol (MCP) server that provides
real-time screen capture streaming using Streamable HTTP protocol with
HTTP/2 optimization and chunked transfer encoding.
"""

__version__ = "2.0.0"
__author__ = "inkbytefo"
__email__ = "<EMAIL>"
__description__ = "Streamable HTTP MCP Server for real-time screen streaming"

# New Streamable HTTP components
from .config import MCPServerConfig, get_config, get_default_config
from .screen_capture import ScreenCapture, CaptureStats
from .streamable_http_server import StreamableHTTPMCPServer

# Transport layer
from .transport import StreamableHTTPTransport, HTTPStreamMessage, StreamStats as TransportStats

# Protocol layer
from .protocol import StreamableHTTPMCPAdapter, MCPMethod, MCPCapabilities

# Streaming layer
from .streaming import HTTPScreenStreamer, HTTPStreamFrame, StreamingStats, StreamQuality

# Legacy components (backward compatibility)
from .streaming import Base64Streamer, StreamFrame, StreamStats, StreamState
from .server import MCPScreenStreamingServer

__all__ = [
    # Core configuration
    "MCPServerConfig",
    "get_config",
    "get_default_config",

    # Screen capture
    "ScreenCapture",
    "CaptureStats",

    # New Streamable HTTP server
    "StreamableHTTPMCPServer",

    # Transport layer
    "StreamableHTTPTransport",
    "HTTPStreamMessage",
    "TransportStats",

    # Protocol layer
    "StreamableHTTPMCPAdapter",
    "MCPMethod",
    "MCPCapabilities",

    # Streaming layer
    "HTTPScreenStreamer",
    "HTTPStreamFrame",
    "StreamingStats",
    "StreamQuality",

    # Legacy components
    "Base64Streamer",
    "StreamFrame",
    "StreamStats",
    "StreamState",
    "MCPScreenStreamingServer"
]

"""
Base64 Streaming MCP Server Package

A high-performance Model Context Protocol (MCP) server that provides 
real-time screen capture streaming in base64 format via WebSocket connections.
"""

__version__ = "1.0.0"
__author__ = "inkbytefo"
__email__ = "<EMAIL>"
__description__ = "MCP Server for real-time screen streaming"

from .config import MCPServerConfig, get_config, get_default_config
from .screen_capture import ScreenCapture, CaptureStats
from .streaming import Base64Streamer, StreamFrame, StreamStats, StreamState
from .server import MCPScreenStreamingServer

__all__ = [
    "MCPServerConfig",
    "get_config", 
    "get_default_config",
    "ScreenCapture",
    "CaptureStats", 
    "Base64Streamer",
    "StreamFrame",
    "StreamStats",
    "StreamState",
    "MCPScreenStreamingServer"
]

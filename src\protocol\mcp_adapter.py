"""
MCP Protocol Adapter for Streamable HTTP

This module provides an adapter layer that bridges the Model Context Protocol (MCP)
with the Streamable HTTP transport, handling JSON-RPC 2.0 message routing and processing.

Features:
- JSON-RPC 2.0 message handling
- MCP capabilities negotiation
- Tool and resource management
- Streaming data integration
- Error handling and validation
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional, List, Callable, AsyncGenerator
from dataclasses import dataclass, asdict
from enum import Enum
import uuid

import structlog
from mcp.server import Server
from mcp.types import (
    Tool, Resource, Prompt, TextContent, ImageContent,
    CallToolResult, ReadResourceResult, GetPromptResult,
    ListToolsResult, ListResourcesResult, ListPromptsResult
)

from ..transport import StreamableHTTPTransport, HTTPStreamMessage
from ..config import MCPServerConfig

logger = structlog.get_logger(__name__)


class MCPMethod(str, Enum):
    """MCP JSON-RPC method names."""
    # Initialization
    INITIALIZE = "initialize"
    INITIALIZED = "initialized"
    
    # Capabilities
    LIST_TOOLS = "tools/list"
    CALL_TOOL = "tools/call"
    LIST_RESOURCES = "resources/list"
    READ_RESOURCE = "resources/read"
    LIST_PROMPTS = "prompts/list"
    GET_PROMPT = "prompts/get"
    
    # Streaming
    STREAM_START = "stream/start"
    STREAM_STOP = "stream/stop"
    STREAM_STATUS = "stream/status"


@dataclass
class MCPCapabilities:
    """MCP server capabilities."""
    tools: bool = True
    resources: bool = True
    prompts: bool = True
    streaming: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "tools": {"listChanged": True} if self.tools else None,
            "resources": {"subscribe": True, "listChanged": True} if self.resources else None,
            "prompts": {"listChanged": True} if self.prompts else None,
            "streaming": {"realtime": True, "chunked": True} if self.streaming else None
        }


class StreamableHTTPMCPAdapter:
    """MCP Protocol Adapter for Streamable HTTP transport."""
    
    def __init__(self, config: MCPServerConfig, transport: StreamableHTTPTransport):
        """Initialize MCP adapter."""
        self.config = config
        self.transport = transport
        self.server = Server(config.server_name, config.server_version)
        self.capabilities = MCPCapabilities()
        
        # State management
        self.initialized = False
        self.client_capabilities: Optional[Dict[str, Any]] = None
        
        # Handler registries
        self.tool_handlers: Dict[str, Callable] = {}
        self.resource_handlers: Dict[str, Callable] = {}
        self.prompt_handlers: Dict[str, Callable] = {}
        self.stream_handlers: Dict[str, Callable] = {}
        
        # Setup MCP handlers
        self._setup_mcp_handlers()
        
        # Register transport handlers
        self._register_transport_handlers()
        
        logger.info(
            "MCP adapter initialized",
            server_name=config.server_name,
            version=config.server_version,
            capabilities=asdict(self.capabilities)
        )
    
    def _setup_mcp_handlers(self):
        """Setup MCP server handlers."""
        
        @self.server.list_tools()
        async def list_tools() -> ListToolsResult:
            """List available tools."""
            tools = []
            for tool_name, handler in self.tool_handlers.items():
                # Get tool metadata from handler
                if hasattr(handler, '__tool_metadata__'):
                    metadata = handler.__tool_metadata__
                    tool = Tool(
                        name=tool_name,
                        description=metadata.get('description', f'Tool: {tool_name}'),
                        inputSchema=metadata.get('inputSchema', {})
                    )
                    tools.append(tool)
            
            return ListToolsResult(tools=tools)
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: Optional[Dict[str, Any]] = None) -> CallToolResult:
            """Call a tool."""
            if name not in self.tool_handlers:
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Tool '{name}' not found")],
                    isError=True
                )
            
            try:
                result = await self.tool_handlers[name](arguments or {})
                
                if isinstance(result, dict):
                    content = [TextContent(type="text", text=json.dumps(result, indent=2))]
                elif isinstance(result, str):
                    content = [TextContent(type="text", text=result)]
                elif isinstance(result, bytes):
                    # Handle binary data (e.g., images)
                    import base64
                    content = [ImageContent(
                        type="image",
                        data=base64.b64encode(result).decode(),
                        mimeType="image/png"
                    )]
                else:
                    content = [TextContent(type="text", text=str(result))]
                
                return CallToolResult(content=content)
                
            except Exception as e:
                logger.error("Tool execution error", tool=name, error=str(e), exc_info=True)
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Tool error: {str(e)}")],
                    isError=True
                )
        
        @self.server.list_resources()
        async def list_resources() -> ListResourcesResult:
            """List available resources."""
            resources = []
            for resource_uri, handler in self.resource_handlers.items():
                if hasattr(handler, '__resource_metadata__'):
                    metadata = handler.__resource_metadata__
                    resource = Resource(
                        uri=resource_uri,
                        name=metadata.get('name', resource_uri),
                        description=metadata.get('description', f'Resource: {resource_uri}'),
                        mimeType=metadata.get('mimeType', 'application/json')
                    )
                    resources.append(resource)
            
            return ListResourcesResult(resources=resources)
        
        @self.server.read_resource()
        async def read_resource(uri: str) -> ReadResourceResult:
            """Read a resource."""
            if uri not in self.resource_handlers:
                return ReadResourceResult(
                    contents=[TextContent(type="text", text=f"Resource '{uri}' not found")]
                )
            
            try:
                result = await self.resource_handlers[uri]()
                
                if isinstance(result, dict):
                    content = TextContent(type="text", text=json.dumps(result, indent=2))
                elif isinstance(result, str):
                    content = TextContent(type="text", text=result)
                elif isinstance(result, bytes):
                    # Handle binary data
                    import base64
                    content = ImageContent(
                        type="image",
                        data=base64.b64encode(result).decode(),
                        mimeType="image/png"
                    )
                else:
                    content = TextContent(type="text", text=str(result))
                
                return ReadResourceResult(contents=[content])
                
            except Exception as e:
                logger.error("Resource read error", uri=uri, error=str(e), exc_info=True)
                return ReadResourceResult(
                    contents=[TextContent(type="text", text=f"Resource error: {str(e)}")]
                )
    
    def _register_transport_handlers(self):
        """Register handlers with the transport layer."""
        
        # MCP protocol methods
        self.transport.register_message_handler(
            MCPMethod.INITIALIZE.value, 
            self._handle_initialize
        )
        self.transport.register_message_handler(
            MCPMethod.LIST_TOOLS.value,
            self._handle_list_tools
        )
        self.transport.register_message_handler(
            MCPMethod.CALL_TOOL.value,
            self._handle_call_tool
        )
        self.transport.register_message_handler(
            MCPMethod.LIST_RESOURCES.value,
            self._handle_list_resources
        )
        self.transport.register_message_handler(
            MCPMethod.READ_RESOURCE.value,
            self._handle_read_resource
        )
        
        # Streaming methods
        self.transport.register_message_handler(
            MCPMethod.STREAM_START.value,
            self._handle_stream_start
        )
        self.transport.register_message_handler(
            MCPMethod.STREAM_STOP.value,
            self._handle_stream_stop
        )
        self.transport.register_message_handler(
            MCPMethod.STREAM_STATUS.value,
            self._handle_stream_status
        )
    
    async def _handle_initialize(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Handle MCP initialization."""
        params = message.get("params", {})
        self.client_capabilities = params.get("capabilities", {})
        
        # Server capabilities response
        server_capabilities = self.capabilities.to_dict()
        
        # Remove None values
        server_capabilities = {k: v for k, v in server_capabilities.items() if v is not None}
        
        self.initialized = True
        
        logger.info(
            "MCP initialized",
            client_capabilities=self.client_capabilities,
            server_capabilities=server_capabilities
        )
        
        return {
            "protocolVersion": "2024-11-05",
            "capabilities": server_capabilities,
            "serverInfo": {
                "name": self.config.server_name,
                "version": self.config.server_version
            }
        }
    
    async def _handle_list_tools(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Handle list tools request."""
        if not self.initialized:
            raise Exception("Server not initialized")
        
        result = await self.server._list_tools_handler()
        return {"tools": [tool.model_dump() for tool in result.tools]}
    
    async def _handle_call_tool(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Handle call tool request."""
        if not self.initialized:
            raise Exception("Server not initialized")
        
        params = message.get("params", {})
        name = params.get("name")
        arguments = params.get("arguments", {})
        
        result = await self.server._call_tool_handler(name, arguments)
        return {
            "content": [content.model_dump() for content in result.content],
            "isError": result.isError
        }
    
    async def _handle_list_resources(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Handle list resources request."""
        if not self.initialized:
            raise Exception("Server not initialized")
        
        result = await self.server._list_resources_handler()
        return {"resources": [resource.model_dump() for resource in result.resources]}
    
    async def _handle_read_resource(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Handle read resource request."""
        if not self.initialized:
            raise Exception("Server not initialized")
        
        params = message.get("params", {})
        uri = params.get("uri")
        
        result = await self.server._read_resource_handler(uri)
        return {"contents": [content.model_dump() for content in result.contents]}
    
    async def _handle_stream_start(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Handle stream start request."""
        params = message.get("params", {})
        stream_type = params.get("type", "screen")
        
        if stream_type in self.stream_handlers:
            # Start streaming
            await self.stream_handlers[stream_type]("start", params)
            return {"status": "started", "type": stream_type}
        else:
            return {"status": "error", "message": f"Stream type '{stream_type}' not supported"}
    
    async def _handle_stream_stop(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Handle stream stop request."""
        params = message.get("params", {})
        stream_type = params.get("type", "screen")
        
        if stream_type in self.stream_handlers:
            await self.stream_handlers[stream_type]("stop", params)
            return {"status": "stopped", "type": stream_type}
        else:
            return {"status": "error", "message": f"Stream type '{stream_type}' not found"}
    
    async def _handle_stream_status(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Handle stream status request."""
        params = message.get("params", {})
        stream_type = params.get("type", "screen")
        
        if stream_type in self.stream_handlers:
            status = await self.stream_handlers[stream_type]("status", params)
            return {"status": status, "type": stream_type}
        else:
            return {"status": "not_found", "type": stream_type}
    
    def register_tool(self, name: str, handler: Callable, metadata: Optional[Dict[str, Any]] = None):
        """Register a tool handler."""
        self.tool_handlers[name] = handler
        if metadata:
            handler.__tool_metadata__ = metadata
        logger.info("Registered tool", name=name)
    
    def register_resource(self, uri: str, handler: Callable, metadata: Optional[Dict[str, Any]] = None):
        """Register a resource handler."""
        self.resource_handlers[uri] = handler
        if metadata:
            handler.__resource_metadata__ = metadata
        logger.info("Registered resource", uri=uri)
    
    def register_stream_handler(self, stream_type: str, handler: Callable):
        """Register a stream handler."""
        self.stream_handlers[stream_type] = handler
        logger.info("Registered stream handler", type=stream_type)
    
    async def start(self):
        """Start the MCP adapter."""
        await self.transport.start()
        logger.info("MCP adapter started")
    
    async def stop(self):
        """Stop the MCP adapter."""
        await self.transport.stop()
        logger.info("MCP adapter stopped")

"""
Base64 streaming module for real-time screen capture data.
Handles encoding, compression, and streaming of screen capture data.
"""

import asyncio
import base64
import gzip
import time
import zstandard as zstd
from typing import Optional, AsyncGenerator, Dict, Any, List
from dataclasses import dataclass, asdict
from enum import Enum

import structlog

from .config import MCPServerConfig, CompressionType, get_config
from .screen_capture import ScreenCapture

logger = structlog.get_logger(__name__)


class StreamState(str, Enum):
    """Stream state enumeration."""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"


@dataclass
class StreamFrame:
    """Represents a single stream frame."""
    timestamp: float
    frame_id: int
    data: str  # Base64 encoded data
    size_bytes: int
    compressed_size: Optional[int] = None
    compression_type: str = "none"
    format: str = "jpeg"
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class StreamStats:
    """Statistics for streaming performance."""
    total_frames: int = 0
    total_bytes: int = 0
    compressed_bytes: int = 0
    avg_fps: float = 0.0
    avg_compression_ratio: float = 0.0
    start_time: float = 0.0
    last_frame_time: float = 0.0
    errors: int = 0


class Base64Streamer:
    """High-performance Base64 streaming for screen capture data."""
    
    def __init__(self, config: Optional[MCPServerConfig] = None):
        """Initialize Base64 streamer with configuration."""
        self.config = config or get_config()
        self.screen_capture = ScreenCapture(self.config)
        self.stats = StreamStats()
        self.state = StreamState.STOPPED
        self._frame_counter = 0
        self._compressor = self._init_compressor()
        
        logger.info(
            "Base64 streamer initialized",
            compression=self.config.compression,
            chunk_size=self.config.chunk_size,
            buffer_size=self.config.buffer_size
        )
    
    def _init_compressor(self):
        """Initialize compression based on configuration."""
        if self.config.compression == CompressionType.ZSTD:
            return zstd.ZstdCompressor(level=3)
        return None
    
    def _compress_data(self, data: bytes) -> tuple[bytes, str]:
        """Compress data based on configuration."""
        if self.config.compression == CompressionType.NONE:
            return data, "none"
        
        elif self.config.compression == CompressionType.GZIP:
            compressed = gzip.compress(data, compresslevel=6)
            return compressed, "gzip"
        
        elif self.config.compression == CompressionType.ZSTD:
            if self._compressor:
                compressed = self._compressor.compress(data)
                return compressed, "zstd"
        
        # Fallback to no compression
        return data, "none"
    
    def _encode_frame(self, frame_data: bytes) -> StreamFrame:
        """Encode frame data to Base64 with compression."""
        try:
            start_time = time.time()
            
            # Compress data if enabled
            compressed_data, compression_type = self._compress_data(frame_data)
            
            # Encode to Base64
            base64_data = base64.b64encode(compressed_data).decode('utf-8')
            
            # Create frame object
            frame = StreamFrame(
                timestamp=time.time(),
                frame_id=self._frame_counter,
                data=base64_data,
                size_bytes=len(frame_data),
                compressed_size=len(compressed_data) if compression_type != "none" else None,
                compression_type=compression_type,
                format=self.config.image_format.value,
                metadata={
                    "encoding_time": time.time() - start_time,
                    "compression_ratio": len(frame_data) / len(compressed_data) if compression_type != "none" else 1.0
                }
            )
            
            self._frame_counter += 1
            
            # Update statistics
            self.stats.total_frames += 1
            self.stats.total_bytes += len(frame_data)
            self.stats.compressed_bytes += len(compressed_data)
            self.stats.last_frame_time = time.time()
            
            if self.stats.start_time > 0:
                elapsed = self.stats.last_frame_time - self.stats.start_time
                self.stats.avg_fps = self.stats.total_frames / elapsed if elapsed > 0 else 0
                self.stats.avg_compression_ratio = self.stats.total_bytes / self.stats.compressed_bytes if self.stats.compressed_bytes > 0 else 1.0
            
            logger.debug(
                "Frame encoded",
                frame_id=frame.frame_id,
                original_size=frame.size_bytes,
                compressed_size=frame.compressed_size,
                compression_ratio=frame.metadata.get("compression_ratio", 1.0),
                encoding_time=frame.metadata.get("encoding_time", 0)
            )
            
            return frame
            
        except Exception as e:
            self.stats.errors += 1
            logger.error("Frame encoding failed", error=str(e), exc_info=True)
            raise
    
    async def start_stream(self) -> None:
        """Start the streaming process."""
        if self.state != StreamState.STOPPED:
            logger.warning("Stream already running or starting")
            return
        
        try:
            self.state = StreamState.STARTING
            self.stats.start_time = time.time()
            
            # Start screen capture
            await self.screen_capture.start_continuous_capture()
            
            self.state = StreamState.RUNNING
            logger.info("Stream started successfully")
            
        except Exception as e:
            self.state = StreamState.ERROR
            logger.error("Failed to start stream", error=str(e), exc_info=True)
            raise
    
    async def stop_stream(self) -> None:
        """Stop the streaming process."""
        if self.state == StreamState.STOPPED:
            return
        
        try:
            self.screen_capture.stop_capture()
            self.state = StreamState.STOPPED
            
            logger.info(
                "Stream stopped",
                stats=asdict(self.stats),
                capture_stats=asdict(self.screen_capture.get_stats())
            )
            
        except Exception as e:
            logger.error("Error stopping stream", error=str(e), exc_info=True)
    
    def pause_stream(self) -> None:
        """Pause the streaming process."""
        if self.state == StreamState.RUNNING:
            self.state = StreamState.PAUSED
            logger.info("Stream paused")
    
    def resume_stream(self) -> None:
        """Resume the streaming process."""
        if self.state == StreamState.PAUSED:
            self.state = StreamState.RUNNING
            logger.info("Stream resumed")
    
    async def get_frame_stream(self) -> AsyncGenerator[StreamFrame, None]:
        """Get async generator for streaming frames."""
        if self.state != StreamState.RUNNING:
            logger.warning("Stream not running, cannot generate frames")
            return
        
        frame_interval = 1.0 / self.config.fps
        last_frame_time = 0.0
        
        while self.state == StreamState.RUNNING:
            try:
                current_time = time.time()
                
                # Check if it's time for next frame
                if current_time - last_frame_time >= frame_interval:
                    # Get latest frame from screen capture
                    frame_data = self.screen_capture.get_latest_frame()
                    
                    if frame_data:
                        # Encode frame
                        stream_frame = self._encode_frame(frame_data)
                        yield stream_frame
                        last_frame_time = current_time
                
                # Small sleep to prevent busy waiting
                await asyncio.sleep(0.001)
                
            except Exception as e:
                self.stats.errors += 1
                logger.error("Error in frame stream", error=str(e), exc_info=True)
                await asyncio.sleep(0.1)  # Longer sleep on error
    
    async def get_batch_frames(self, max_frames: int = 10) -> List[StreamFrame]:
        """Get a batch of frames for bulk processing."""
        if self.state != StreamState.RUNNING:
            return []
        
        try:
            # Get all available frames from capture buffer
            frame_data_list = self.screen_capture.get_all_frames()
            
            # Limit to max_frames
            frame_data_list = frame_data_list[-max_frames:] if len(frame_data_list) > max_frames else frame_data_list
            
            # Encode all frames
            stream_frames = []
            for frame_data in frame_data_list:
                stream_frame = self._encode_frame(frame_data)
                stream_frames.append(stream_frame)
            
            return stream_frames
            
        except Exception as e:
            self.stats.errors += 1
            logger.error("Error getting batch frames", error=str(e), exc_info=True)
            return []
    
    def get_stream_info(self) -> Dict[str, Any]:
        """Get current stream information."""
        return {
            "state": self.state.value,
            "config": {
                "fps": self.config.fps,
                "compression": self.config.compression.value,
                "image_format": self.config.image_format.value,
                "max_width": self.config.max_width,
                "max_height": self.config.max_height,
            },
            "stats": asdict(self.stats),
            "capture_stats": asdict(self.screen_capture.get_stats()),
            "monitor_info": self.screen_capture.get_monitor_info()
        }
    
    def reset_stats(self) -> None:
        """Reset streaming statistics."""
        self.stats = StreamStats()
        self.screen_capture.reset_stats()
        self._frame_counter = 0
    
    def update_config(self, new_config: MCPServerConfig) -> None:
        """Update configuration (requires restart for some changes)."""
        old_compression = self.config.compression
        self.config = new_config
        
        # Reinitialize compressor if compression changed
        if old_compression != new_config.compression:
            self._compressor = self._init_compressor()
        
        # Update screen capture config
        self.screen_capture.config = new_config
        
        logger.info("Configuration updated", new_config=new_config.dict())
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start_stream()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.stop_stream()
